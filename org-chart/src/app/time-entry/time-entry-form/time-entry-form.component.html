<div class="time-entry-form-container">
  <h2 mat-dialog-title>
    {{ isEditMode ? 'Edit Time Entry' : 'Add Time Entry' }}
  </h2>

  <!-- Toggle buttons container -->
  <div class="toggle-container">
    <!-- Toggle button for leads/managers -->
    <mat-slide-toggle
      *ngIf="isLeadOrManager"
      color="primary"
      [checked]="isOnBehalfMode"
      (change)="toggleOnBehalfMode()"
      [disabled]="isEditMode"
      class="toggle-button">
      {{ isOnBehalfMode ? 'On Behalf of Team Member' : 'My Time Entry' }}
    </mat-slide-toggle>

    <!-- Toggle button for overtime - only visible when FTech project is selected -->
    <mat-slide-toggle
      *ngIf="showOvertimeToggle"
      color="accent"
      [checked]="isOvertimeEntry"
      (change)="toggleOvertimeMode()"
      class="toggle-button">
      {{ isOvertimeEntry ? 'Overtime Entry' : 'Regular Entry' }}
    </mat-slide-toggle>
  </div>

  <form [formGroup]="timeEntryForm" (ngSubmit)="onSubmit()">
    <div mat-dialog-content>
      <div class="form-row">
        <!-- Date Worked -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Date Worked</mat-label>
          <input matInput [matDatepicker]="datePicker" formControlName="dateWorked" required>
          <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
          <mat-datepicker #datePicker></mat-datepicker>
          <mat-error *ngIf="timeEntryForm.get('dateWorked')?.hasError('required')">
            Date Worked is required
          </mat-error>
        </mat-form-field>

        <!-- User ID Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>User ID</mat-label>
          <input matInput formControlName="userIdField" required [readonly]="!isOnBehalfMode">
          <mat-error *ngIf="timeEntryForm.get('userIdField')?.hasError('required')">
            User ID is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Reporting Senior's UserID -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Reporting Senior's UserID</mat-label>
          <input matInput formControlName="reportingSeniorUserId" required>
          <mat-error *ngIf="timeEntryForm.get('reportingSeniorUserId')?.hasError('required')">
            Reporting Senior's UserID is required
          </mat-error>
        </mat-form-field>

        <!-- Resource Name -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Resource Name</mat-label>
          <input matInput formControlName="resourceName" placeholder="lastname, firstname">
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Company -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Company</mat-label>
          <input matInput formControlName="company" value="ABC" readonly>
        </mat-form-field>

        <!-- Type -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Type</mat-label>
          <mat-select formControlName="type" required>
            <mat-option value="Ticket">Ticket</mat-option>
            <mat-option value="Meeting">Meeting</mat-option>
            <mat-option value="Training">Training</mat-option>
            <mat-option value="Other">Other</mat-option>
          </mat-select>
          <mat-error *ngIf="timeEntryForm.get('type')?.hasError('required')">
            Type is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Lead Selection -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Lead</mat-label>
          <mat-select formControlName="leadId" required>
            <div class="search-box">
              <mat-form-field appearance="outline" class="search-field">
                <input matInput (keyup)="filterLeads($event)" placeholder="Search leads...">
              </mat-form-field>
            </div>
            <mat-option *ngFor="let lead of filteredLeads" [value]="lead.id">
              {{ lead.ldap }} ({{ lead.role }})
            </mat-option>
          </mat-select>
          <mat-error *ngIf="timeEntryForm.get('leadId')?.hasError('required')">
            Lead is required
          </mat-error>
        </mat-form-field>

        <!-- Project Selection -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Project</mat-label>
          <mat-select formControlName="projectId" required>
            <mat-option *ngFor="let project of projects" [value]="project.projectId || project.id">
              {{ project.projectName || project.name }} ({{ project.projectCode || project.code }})
              <span *ngIf="(project.projectName || project.name) === 'FTech'" class="ftech-indicator"> - Overtime Eligible</span>
            </mat-option>
          </mat-select>
          <mat-hint *ngIf="showOvertimeToggle" class="overtime-hint">Overtime toggle is available for this project</mat-hint>
          <mat-error *ngIf="timeEntryForm.get('projectId')?.hasError('required')">
            Project is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Process -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Process</mat-label>
          <input matInput formControlName="process" required>
          <mat-error *ngIf="timeEntryForm.get('process')?.hasError('required')">
            Process is required
          </mat-error>
        </mat-form-field>

        <!-- Activity -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Activity</mat-label>
          <mat-select formControlName="activity" required>
            <mat-option *ngFor="let activity of activities" [value]="activity.value">
              {{ activity.name }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="timeEntryForm.get('activity')?.hasError('required')">
            Activity is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Time in Minutes -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Time in Minutes</mat-label>
          <input matInput type="number" min="1" [max]="maxTimeInMins" formControlName="timeInMins" required>
          <mat-error *ngIf="timeEntryForm.get('timeInMins')?.hasError('required')">
            Time is required
          </mat-error>
          <mat-error *ngIf="timeEntryForm.get('timeInMins')?.hasError('min')">
            Time must be at least 1 minute
          </mat-error>
          <mat-error *ngIf="timeEntryForm.get('timeInMins')?.hasError('max')">
            Time cannot exceed {{ maxTimeInMins }} minutes (8 hours)
          </mat-error>
        </mat-form-field>

        <!-- Attendance Type -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Attendance Type</mat-label>
          <mat-select formControlName="attendanceType" required>
            <mat-option *ngFor="let type of filteredAttendanceTypes" [value]="type.value">
              {{ type.name }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="timeEntryForm.get('attendanceType')?.hasError('required')">
            Attendance Type is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Comment -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Comment</mat-label>
          <textarea matInput formControlName="comment" rows="3"></textarea>
        </mat-form-field>
      </div>
    </div>

    <div mat-dialog-actions align="end">
      <button mat-button type="button" (click)="onCancel()">Cancel</button>
      <button mat-raised-button color="primary" type="submit" [disabled]="timeEntryForm.invalid">
        {{ isEditMode ? 'Update' : 'Submit' }}
      </button>
    </div>
  </form>
</div>
